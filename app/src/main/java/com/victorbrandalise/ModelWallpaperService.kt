package com.victorbrandalise

import android.service.wallpaper.WallpaperService
import android.view.SurfaceHolder
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import com.google.android.filament.utils.Utils

class ModelWallpaperService : WallpaperService() {

    companion object {
        init {
            Utils.init()
        }
    }

    override fun onCreateEngine(): Engine {
        return ModelWallpaperEngine()
    }

    inner class ModelWallpaperEngine : Engine(), LifecycleOwner {
        private lateinit var lifecycleRegistry: LifecycleRegistry
        private var modelRenderer: ModelRenderer? = null

        override fun onCreate(surfaceHolder: SurfaceHolder?) {
            super.onCreate(surfaceHolder)
            lifecycleRegistry = LifecycleRegistry(this)
            lifecycleRegistry.currentState = Lifecycle.State.CREATED
        }

        override fun onSurfaceCreated(holder: SurfaceHolder) {
            super.onSurfaceCreated(holder)

            modelRenderer = ModelRenderer()
            modelRenderer?.onWallpaperSurfaceAvailable(this@ModelWallpaperService, holder, lifecycle)

            lifecycleRegistry.currentState = Lifecycle.State.STARTED
        }

        override fun onVisibilityChanged(visible: Boolean) {
            super.onVisibilityChanged(visible)
            if (visible) {
                lifecycleRegistry.currentState = Lifecycle.State.RESUMED
            } else {
                lifecycleRegistry.currentState = Lifecycle.State.STARTED
            }
        }

        override fun onSurfaceDestroyed(holder: SurfaceHolder) {
            super.onSurfaceDestroyed(holder)
            lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
            modelRenderer = null
        }

        override val lifecycle: Lifecycle
            get() = lifecycleRegistry
    }
}
