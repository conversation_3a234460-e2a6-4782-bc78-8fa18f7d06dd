plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'org.jetbrains.kotlin.plugin.compose'
}

android {
    namespace 'com.victorbrandalise'
    compileSdk 36

    defaultConfig {
        applicationId "com.victorbrandalise"
        minSdk 34
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.9.0'

    implementation 'com.google.android.material:material:1.12.0'

    implementation platform('androidx.compose:compose-bom:2025.05.01')
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.material:material"
    implementation "androidx.compose.ui:ui-tooling-preview"
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.activity:activity-compose:1.10.1'

    implementation 'com.google.android.filament:filament-android:1.57.1'
    implementation 'com.google.android.filament:filament-utils-android:1.57.1'
    implementation 'com.google.android.filament:gltfio-android:1.57.1'

    debugImplementation "androidx.compose.ui:ui-tooling"
    debugImplementation "androidx.compose.ui:ui-test-manifest"
}